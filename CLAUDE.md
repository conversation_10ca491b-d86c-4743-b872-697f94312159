# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Langflow Overview

Langflow is a visual AI workflow builder with both GUI and API/MCP server capabilities for creating and deploying AI-powered agents and workflows. It combines a React-based visual interface with a Python backend built on FastAPI and LangChain.

## Architecture

### Monorepo Structure
- **Main package**: `langflow` (v1.5.0) - Full application
- **Base package**: `langflow-base` (v0.5.0) - Core functionality in `src/backend/base/`
- **Embedded chat**: Separate package in `langflow-embedded-chat/`

### Backend Architecture
- **Component System**: All components inherit from `CustomComponent` base class
- **Service Layer**: Dependency injection pattern with services for database, storage, tracing
- **Graph Processing**: Flows execute as directed graphs with vertices and edges
- **API Design**: RESTful APIs (v1 and v2) plus WebSocket for real-time updates

### Frontend Architecture
- **State Management**: Zustand stores in `src/frontend/src/stores/`
- **UI Framework**: shadcn/ui components with Tailwind CSS
- **Flow Builder**: XYFlow (ReactFlow) for visual programming interface
- **Build Tool**: Vite with TypeScript

## Essential Commands

### Quick Start
```bash
make init      # Install all dependencies and build frontend
make backend   # Run backend dev server (port 7860)
make frontend  # Run frontend dev server (port 3000)
```

### Testing
```bash
make unit_tests         # Backend unit tests
make integration_tests  # Backend integration tests
make test_frontend      # Frontend Jest tests
make tests_frontend     # Frontend Playwright e2e tests
```

### Code Quality
```bash
make lint               # Run mypy type checking
make format_backend     # Format Python code with ruff
make format_frontend    # Format JS/TS code with Biome
```

### Database
```bash
make alembic-revision message="description"  # Create migration
make alembic-upgrade                        # Apply migrations
make alembic-downgrade                      # Rollback one migration
```

### Building
```bash
make build_frontend     # Build frontend static files
make build_langflow     # Build Python package
```

## Development Workflow

### Adding Components
1. Create component in `src/backend/base/langflow/components/{category}/`
2. Inherit from `CustomComponent` or specific base class
3. Define inputs, outputs, and build method
4. Add to category's `__init__.py` alphabetically
5. Test in UI first, then commit

### Frontend Development
1. Components in `src/frontend/src/components/`
2. Pages in `src/frontend/src/pages/`
3. API calls via `src/frontend/src/controllers/API/`
4. State management with Zustand stores

### Backend Development
1. API routes in `src/backend/langflow/api/`
2. Services in `src/backend/langflow/services/`
3. Database models in `src/backend/langflow/services/database/models/`
4. Use dependency injection for services

## Key Patterns

### Component Structure
```python
from langflow.custom import CustomComponent
from langflow.io import MessageTextInput, Output

class MyComponent(CustomComponent):
    display_name = "My Component"
    description = "Component description"
    
    inputs = [
        MessageTextInput(
            name="input_text",
            display_name="Input",
        ),
    ]
    
    outputs = [
        Output(display_name="Output", name="output", method="build"),
    ]
    
    def build(self, input_text: str) -> str:
        return f"Processed: {input_text}"
```

### API Endpoint Pattern
```python
from fastapi import APIRouter, Depends
from langflow.services.deps import get_session

router = APIRouter(prefix="/api/v1", tags=["my_feature"])

@router.get("/endpoint")
async def my_endpoint(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_active_user),
):
    # Implementation
```

## Configuration

- **Environment**: Copy `.env.example` to `.env`
- **Python**: Requires 3.10-3.13, uses `uv` package manager
- **Node**: Requires v22.12 LTS for frontend
- **Database**: SQLite by default, PostgreSQL for production

## Common Tasks

### Running Single Test
```bash
# Backend
pytest src/backend/tests/unit/test_specific.py::test_name -xvs

# Frontend
npm test -- src/frontend/tests/specific.test.ts
```

### Debugging
- Backend: Set `LANGFLOW_LOG_LEVEL=debug`
- Frontend: Use React DevTools and browser debugger
- API: Check `/api/v1/docs` for Swagger UI

### Building Docker Image
```bash
make docker_build
# Or use docker-compose in docker_example/
```

## Important Notes

1. **File Mode Changes**: Some starter project files auto-format on run
2. **Port 7860**: Backend automatically kills existing processes on this port
3. **Pre-commit**: Install hooks with `uv run pre-commit install`
4. **Testing**: Some database tests may fail in parallel execution
5. **Windows**: Must use WSL for development

## Key Dependencies

- **Backend**: FastAPI, LangChain, SQLModel, Pydantic
- **Frontend**: React, TypeScript, XYFlow, Zustand, shadcn/ui
- **Database**: Alembic for migrations, SQLModel ORM
- **Testing**: Pytest (backend), Jest/Playwright (frontend)